// Test script to demonstrate memory functionality of the logger
// This script shows how the logger manages memory by keeping only the last 1000 log entries

import { logger, LogLevel } from './src/lib/logger.ts';

console.log('🧠 Testing Memory Management in Logger Utility');
console.log('================================================');

// Test 1: Basic logging functionality
console.log('\n1. Testing basic logging functionality:');
logger.info('Application started', { version: '1.0.0' }, 'APP');
logger.debug('Debug information', { userId: 123 }, 'USER');
logger.warn('Warning message', { component: 'ContactForm' }, 'FORM');
logger.error('Error occurred', { error: 'Network timeout' }, 'NETWORK');

// Test 2: Memory management - add more than 1000 logs
console.log('\n2. Testing memory management (adding 1200 logs):');
const startTime = Date.now();

for (let i = 0; i < 1200; i++) {
  logger.info(`Log entry ${i}`, { iteration: i, batch: Math.floor(i / 100) }, 'MEMORY_TEST');
}

const endTime = Date.now();
console.log(`✅ Added 1200 log entries in ${endTime - startTime}ms`);

// Test 3: Check memory constraint
const allLogs = logger.getLogs();
console.log(`📊 Total logs in memory: ${allLogs.length} (should be max 1000)`);
console.log(`🔍 First log entry: "${allLogs[0].message}"`);
console.log(`🔍 Last log entry: "${allLogs[allLogs.length - 1].message}"`);

// Test 4: Filter logs by level
console.log('\n3. Testing log filtering by level:');
const errorLogs = logger.getLogs(LogLevel.ERROR);
const warnLogs = logger.getLogs(LogLevel.WARN);
const infoLogs = logger.getLogs(LogLevel.INFO);

console.log(`❌ Error logs: ${errorLogs.length}`);
console.log(`⚠️  Warning+ logs: ${warnLogs.length}`);
console.log(`ℹ️  Info+ logs: ${infoLogs.length}`);

// Test 5: Memory usage estimation
const estimatedMemoryUsage = JSON.stringify(allLogs).length;
console.log(`\n4. Memory usage estimation:`);
console.log(`📏 Estimated memory usage: ~${(estimatedMemoryUsage / 1024).toFixed(2)} KB`);
console.log(`📈 Average bytes per log entry: ~${(estimatedMemoryUsage / allLogs.length).toFixed(2)} bytes`);

// Test 6: Clear logs and verify memory cleanup
console.log('\n5. Testing memory cleanup:');
logger.clearLogs();
const logsAfterClear = logger.getLogs();
console.log(`🧹 Logs after clearing: ${logsAfterClear.length} (should be 0)`);

console.log('\n✅ Memory analysis complete!');
console.log('The logger successfully manages memory by:');
console.log('  - Limiting to 1000 log entries maximum');
console.log('  - Using circular buffer (oldest entries are removed)');
console.log('  - Providing memory cleanup functionality');
console.log('  - Efficient filtering without copying all data');
