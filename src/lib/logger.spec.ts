import { describe, it, expect, beforeEach } from 'vitest';
import { Logger, LogLevel, logger } from './logger';

describe('Logger Memory Management', () => {
  let testLogger: Logger;

  beforeEach(() => {
    testLogger = new Logger(LogLevel.DEBUG);
  });

  it('should maintain maximum of 1000 log entries', () => {
    // Add more than 1000 logs
    for (let i = 0; i < 1200; i++) {
      testLogger.info(`Log entry ${i}`, { iteration: i });
    }

    const logs = testLogger.getLogs();
    expect(logs.length).toBe(1000);
    
    // Should keep the most recent logs (200-1199)
    expect(logs[0].message).toBe('Log entry 200');
    expect(logs[logs.length - 1].message).toBe('Log entry 1199');
  });

  it('should filter logs by level correctly', () => {
    testLogger.debug('Debug message');
    testLogger.info('Info message');
    testLogger.warn('Warning message');
    testLogger.error('Error message');

    const errorLogs = testLogger.getLogs(LogLevel.ERROR);
    const warnLogs = testLogger.getLogs(LogLevel.WARN);
    const infoLogs = testLogger.getLogs(LogLevel.INFO);
    const debugLogs = testLogger.getLogs(LogLevel.DEBUG);

    expect(errorLogs.length).toBe(1);
    expect(warnLogs.length).toBe(2); // warn + error
    expect(infoLogs.length).toBe(3); // info + warn + error
    expect(debugLogs.length).toBe(4); // all logs
  });

  it('should clear logs and free memory', () => {
    // Add some logs
    for (let i = 0; i < 100; i++) {
      testLogger.info(`Test log ${i}`);
    }

    expect(testLogger.getLogs().length).toBe(100);

    testLogger.clearLogs();
    expect(testLogger.getLogs().length).toBe(0);
  });

  it('should store log entries with proper structure', () => {
    const testData = { userId: 123, action: 'login' };
    testLogger.info('User logged in', testData, 'AUTH');

    const logs = testLogger.getLogs();
    const log = logs[0];

    expect(log.level).toBe(LogLevel.INFO);
    expect(log.message).toBe('User logged in');
    expect(log.data).toEqual(testData);
    expect(log.context).toBe('AUTH');
    expect(log.timestamp).toBeInstanceOf(Date);
  });

  it('should respect log level filtering during addition', () => {
    const warnLogger = new Logger(LogLevel.WARN);
    
    warnLogger.debug('Debug message'); // Should be ignored
    warnLogger.info('Info message');   // Should be ignored
    warnLogger.warn('Warning message'); // Should be logged
    warnLogger.error('Error message');  // Should be logged

    const logs = warnLogger.getLogs();
    expect(logs.length).toBe(2);
    expect(logs[0].message).toBe('Warning message');
    expect(logs[1].message).toBe('Error message');
  });

  it('should handle memory efficiently with large data objects', () => {
    const largeData = {
      users: Array.from({ length: 1000 }, (_, i) => ({ id: i, name: `User ${i}` })),
      metadata: { timestamp: Date.now(), version: '1.0.0' }
    };

    // Add logs with large data
    for (let i = 0; i < 10; i++) {
      testLogger.info(`Large data log ${i}`, largeData);
    }

    const logs = testLogger.getLogs();
    expect(logs.length).toBe(10);
    expect(logs[0].data.users.length).toBe(1000);
  });

  it('should maintain singleton logger instance', () => {
    // Test that the exported logger is a singleton
    logger.info('Test message 1');
    const logs1 = logger.getLogs();
    
    logger.info('Test message 2');
    const logs2 = logger.getLogs();
    
    expect(logs2.length).toBe(logs1.length + 1);
    expect(logs2[logs2.length - 1].message).toBe('Test message 2');
  });
});
