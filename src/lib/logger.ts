// Logging utility for the application
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

interface LogEntry {
  level: LogLevel;
  message: string;
  data?: any;
  timestamp: Date;
  context?: string;
}

class Logger {
  private logLevel: LogLevel;
  private logs: LogEntry[] = [];
  private maxLogs = 1000; // Keep last 1000 logs in memory

  constructor(level: LogLevel = LogLevel.INFO) {
    this.logLevel = level;
  }

  private addLog(
    level: LogLevel,
    message: string,
    data?: any,
    context?: string,
  ) {
    const entry: LogEntry = {
      level,
      message,
      data,
      timestamp: new Date(),
      context,
    };

    this.logs.push(entry);

    // Keep only the last maxLogs entries
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Also output to console in development
    if (import.meta.env.DEV) {
      const prefix = context ? `[${context}]` : "";
      const timestamp = entry.timestamp.toISOString();

      switch (level) {
        case LogLevel.DEBUG:
          console.debug(`${timestamp} ${prefix} ${message}`, data || "");
          break;
        case LogLevel.INFO:
          console.info(`${timestamp} ${prefix} ${message}`, data || "");
          break;
        case LogLevel.WARN:
          console.warn(`${timestamp} ${prefix} ${message}`, data || "");
          break;
        case LogLevel.ERROR:
          console.error(`${timestamp} ${prefix} ${message}`, data || "");
          break;
      }
    }
  }

  debug(message: string, data?: any, context?: string) {
    if (this.logLevel <= LogLevel.DEBUG) {
      this.addLog(LogLevel.DEBUG, message, data, context);
    }
  }

  info(message: string, data?: any, context?: string) {
    if (this.logLevel <= LogLevel.INFO) {
      this.addLog(LogLevel.INFO, message, data, context);
    }
  }

  warn(message: string, data?: any, context?: string) {
    if (this.logLevel <= LogLevel.WARN) {
      this.addLog(LogLevel.WARN, message, data, context);
    }
  }

  error(message: string, data?: any, context?: string) {
    if (this.logLevel <= LogLevel.ERROR) {
      this.addLog(LogLevel.ERROR, message, data, context);
    }
  }

  // Get logs for debugging purposes
  getLogs(level?: LogLevel): LogEntry[] {
    if (level !== undefined) {
      return this.logs.filter((log) => log.level >= level);
    }
    return [...this.logs];
  }

  // Clear logs
  clearLogs() {
    this.logs = [];
  }

  // Set log level
  setLogLevel(level: LogLevel) {
    this.logLevel = level;
  }
}

// Create a singleton instance
export const logger = new Logger(
  import.meta.env.DEV ? LogLevel.DEBUG : LogLevel.INFO,
);

// Export the Logger class for testing
export { Logger };
