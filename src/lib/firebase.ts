// Firebase configuration
import { initializeApp } from "firebase/app";
import {
  getFirestore,
  collection,
  addDoc,
  serverTimestamp,
  connectFirestoreEmulator,
} from "firebase/firestore";
import { getFunctions, httpsCallable } from "firebase/functions";

// Your Firebase configuration
// Replace with your actual Firebase config
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
};

// Validate required configuration
const requiredConfig = [
  "apiKey",
  "authDomain",
  "projectId",
  "storageBucket",
  "messagingSenderId",
  "appId",
];
const missingConfig = requiredConfig.filter(
  (key) => !firebaseConfig[key as keyof typeof firebaseConfig],
);

if (missingConfig.length > 0) {
  throw new Error(
    `Missing Firebase configuration: ${missingConfig.join(", ")}`,
  );
}

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firestore
export const db = getFirestore(app);

// Initialize Cloud Functions
export const functions = getFunctions(app);

// Contact form data interface
export interface ContactFormData {
  name: string;
  email: string;
  phone: string;
  state: string;
  country: string;
  city: string;
  message: string;
}

// Location info interface
export interface LocationInfo {
  detected: boolean;
  country: string;
  region: string;
  city: string;
  timezone: string;
  currency: string;
  userCountry: string;
  userCity: string;
}

// Complete submission data interface
export interface ContactSubmission {
  formData: ContactFormData;
  locationInfo: LocationInfo;
  userAgent: string;
  language: string;
  timestamp: any; // Firestore timestamp
  ipAddress?: string;
  referrer?: string;
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
  status: "pending" | "processed" | "failed";
  emailSent: boolean;
  emailSentAt?: any;
}

// Helper function to remove undefined values from object
const removeUndefinedValues = (obj: any): any => {
  const cleaned: any = {};
  Object.keys(obj).forEach((key) => {
    if (obj[key] !== undefined && obj[key] !== null) {
      cleaned[key] = obj[key];
    }
  });
  return cleaned;
};

// Function to save contact form data to Firestore
export const saveContactFormToFirestore = async (
  formData: ContactFormData,
  locationInfo: LocationInfo,
  language: string,
): Promise<{ success: boolean; docId?: string; error?: string }> => {
  try {
    // Get UTM parameters and other analytics data
    const urlParams = new URLSearchParams(window.location.search);
    const utmSource = urlParams.get("utm_source");
    const utmMedium = urlParams.get("utm_medium");
    const utmCampaign = urlParams.get("utm_campaign");

    // Get client IP address
    let ipAddress: string | undefined;
    try {
      const response = await fetch("https://api.ipify.org?format=json");
      const data = await response.json();
      ipAddress = data.ip;
    } catch (error) {
      // IP address fetch failed, continue without it
    }

    // Prepare submission data with only defined values
    const submissionData = removeUndefinedValues({
      formData,
      locationInfo,
      userAgent: navigator.userAgent,
      language,
      ipAddress,
      referrer: document.referrer || null,
      utmSource,
      utmMedium,
      utmCampaign,
      status: "pending",
      emailSent: false,
    });

    // Add document to Firestore
    try {
      const docRef = await addDoc(collection(db, "contactSubmissions"), {
        ...submissionData,
        timestamp: serverTimestamp(),
      });

      return {
        success: true,
        docId: docRef.id,
      };
    } catch (firestoreError) {
      // Re-throw to be caught by outer try-catch
      throw firestoreError;
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
};

// Function to send email notifications via Cloud Functions
export const sendEmailNotifications = async (
  docId: string,
  formData: ContactFormData,
  locationInfo: LocationInfo,
): Promise<{ success: boolean; error?: string }> => {
  try {
    // Call Cloud Function to send emails
    const sendContactEmail = httpsCallable(functions, "sendContactEmail");

    const result = await sendContactEmail({
      docId,
      formData,
      locationInfo,
    });

    return {
      success: true,
    };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to send email notifications",
    };
  }
};

// Function to handle complete contact form submission
export const handleContactFormSubmission = async (
  formData: ContactFormData,
  locationInfo: LocationInfo,
  language: string,
): Promise<{ success: boolean; message: string; docId?: string }> => {
  try {
    // Step 1: Save to Firestore
    const firestoreResult = await saveContactFormToFirestore(
      formData,
      locationInfo,
      language,
    );

    if (!firestoreResult.success) {
      return {
        success: false,
        message: `Failed to save form data: ${firestoreResult.error}`,
      };
    }

    // Step 2: Send email notifications
    const emailResult = await sendEmailNotifications(
      firestoreResult.docId!,
      formData,
      locationInfo,
    );

    if (!emailResult.success) {
      // Form was saved but email failed - still consider it a success
      return {
        success: true,
        message: "Your message has been saved. We will contact you soon.",
        docId: firestoreResult.docId,
      };
    }

    return {
      success: true,
      message:
        "Your message has been sent successfully! We will contact you soon.",
      docId: firestoreResult.docId,
    };
  } catch (error) {
    return {
      success: false,
      message: "An unexpected error occurred. Please try again.",
    };
  }
};

// Utility function to format contact data for email
export const formatContactDataForEmail = (
  formData: ContactFormData,
  locationInfo: LocationInfo,
) => {
  return {
    customerName: formData.name,
    customerEmail: formData.email,
    customerPhone: formData.phone || "Not provided",
    customerLocation: `${formData.city ? formData.city + ", " : ""}${formData.state ? formData.state + ", " : ""}${formData.country}`,
    detectedLocation: locationInfo.detected
      ? `${locationInfo.city ? locationInfo.city + ", " : ""}${locationInfo.country}`
      : "Not detected",
    message: formData.message,
    timezone: locationInfo.timezone,
    currency: locationInfo.currency,
    timestamp: new Date().toLocaleString(),
  };
};
