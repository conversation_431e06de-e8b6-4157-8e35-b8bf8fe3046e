import { Link } from "react-router-dom";

const Footer = () => {
  return (
    <footer className="bg-black text-white py-12 w-full">
      <div className="w-full px-4 sm:px-6 lg:px-8">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="md:col-span-2">
            <div className="flex items-center mb-6">
              <div className="w-10 h-10 bg-brand-red rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">X</span>
              </div>
              <span className="ml-3 text-2xl font-bold">XYZ Company</span>
            </div>
            <p className="text-gray-300 mb-6 max-w-md">
              XYZ Company is a global software company specializing in COLD
              FORMED steel building design software. We make it EASY to design,
              sell, engineer, manufacture, and build COLD FORMED steel
              buildings.
            </p>
            <div className="flex space-x-4">
              <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center">
                <span className="text-xs">f</span>
              </div>
              <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center">
                <span className="text-xs">in</span>
              </div>
              <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center">
                <span className="text-xs">tw</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-bold mb-6">Solutions</h4>
            <div className="space-y-3">
              <Link
                to="/manufacturing"
                className="block text-gray-300 hover:text-brand-red transition-colors"
              >
                Manufacturing
              </Link>
              <Link
                to="/selling"
                className="block text-gray-300 hover:text-brand-red transition-colors"
              >
                Selling
              </Link>
              <Link
                to="/engineering"
                className="block text-gray-300 hover:text-brand-red transition-colors"
              >
                Engineering
              </Link>
              <Link
                to="/building"
                className="block text-gray-300 hover:text-brand-red transition-colors"
              >
                Building
              </Link>
              <Link
                to="/buying"
                className="block text-gray-300 hover:text-brand-red transition-colors"
              >
                Buying
              </Link>
            </div>
          </div>

          {/* Company Links */}
          <div>
            <h4 className="text-lg font-bold mb-6">Company</h4>
            <div className="space-y-3">
              <Link
                to="/contact"
                className="block text-gray-300 hover:text-brand-red transition-colors"
              >
                Contact
              </Link>
              <Link
                to="/blog"
                className="block text-gray-300 hover:text-brand-red transition-colors"
              >
                Blog
              </Link>
              <a
                href="#"
                className="block text-gray-300 hover:text-brand-red transition-colors"
              >
                Support
              </a>
              <Link
                to="/privacy"
                className="block text-gray-300 hover:text-brand-red transition-colors"
              >
                Privacy Policy
              </Link>
              <Link
                to="/terms"
                className="block text-gray-300 hover:text-brand-red transition-colors"
              >
                Terms of Service
              </Link>
              <Link
                to="/cookies"
                className="block text-gray-300 hover:text-brand-red transition-colors"
              >
                Cookie Policy
              </Link>
              <Link
                to="/email-policy"
                className="block text-gray-300 hover:text-brand-red transition-colors"
              >
                Email Policy
              </Link>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-8 pt-8 text-center">
          <p className="text-gray-400">
            © 2025 XYZ Company. All Rights Reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
