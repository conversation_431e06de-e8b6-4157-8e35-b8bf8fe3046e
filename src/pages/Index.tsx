import { <PERSON>, useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Globe,
  Code,
  Users,
  Zap,
  Shield,
  CheckCircle,
  Play,
  ArrowRight,
  Building,
  Wrench,
  Calculator,
  ChevronRight,
  Settings,
  TrendingUp,
  FileText,
  DollarSign,
} from "lucide-react";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";

const Index = () => {
  const navigate = useNavigate();

  return (
    <>
      <div className="min-h-screen bg-white">
        <Navigation />

        {/* Hero Section */}
        <section
          id="home"
          className="relative overflow-hidden bg-gradient-to-br from-gray-50 to-white pt-16 pb-20"
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="lg:pr-8">
                <Badge className="mb-6 bg-brand-red/10 text-brand-red border-brand-red/20">
                  COLD FORMED Steel Buildings
                </Badge>
                <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                  We Make COLD FORMED Buildings
                  <span className="text-brand-red"> Easy</span>
                </h1>
                <p className="text-xl text-gray-600 mb-6 leading-relaxed">
                  XYZ Company is a global software company that specializes in
                  designing COLD FORMED steel buildings. We have been making
                  COLD FORMED buildings easy for over 20 years.
                </p>
                <div className="grid grid-cols-2 gap-4 mb-8">
                  {[
                    "Easy to Make",
                    "Easy to Sell",
                    "Easy to Engineer",
                    "Easy to Build",
                    "Easy to Buy",
                  ].map((item, index) => (
                    <div key={index} className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-brand-red mr-2 flex-shrink-0" />
                      <span className="text-gray-700 font-medium">{item}</span>
                    </div>
                  ))}
                </div>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Button
                    size="lg"
                    className="bg-brand-red hover:bg-brand-red-dark text-white"
                  >
                    Try 3D Designer Free
                    <ChevronRight className="ml-2 h-5 w-5" />
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="border-gray-300 text-gray-700 hover:bg-gray-50"
                  >
                    <Play className="mr-2 h-5 w-5" />
                    Watch Demo
                  </Button>
                </div>
              </div>
              <div className="relative">
                <div className="aspect-video bg-gray-100 rounded-2xl flex items-center justify-center">
                  <div className="text-center">
                    <Play className="h-16 w-16 text-brand-red mx-auto mb-4" />
                    <p className="text-gray-600">Company Overview Video</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* About Us Section */}
        <section id="about" className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Common Pain Points */}
            <div className="mb-20">
              <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-8 text-center">
                Common Pain Points in Cold-Formed Steel
              </h3>
              <p className="text-lg text-gray-600 mb-12 text-center max-w-3xl mx-auto">
                People working with cold-formed steel buildings often struggle
                with:
              </p>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[
                  {
                    title: "Complex and Time-Consuming Engineering",
                    description:
                      "Traditional engineering calculations for cold-formed steel can be slow, manual, and require specialist knowledge.",
                  },
                  {
                    title: "Fragmented Design Process",
                    description:
                      "Designers, sellers, engineers, and builders often work in disconnected systems, causing delays and miscommunication.",
                  },
                  {
                    title: "Slow Quoting and Approval Cycles",
                    description:
                      "Long back-and-forth processes frustrate both builders and customers.",
                  },
                  {
                    title: "Limited Customer Engagement",
                    description:
                      "Customers often cannot visualize or customize their building easily, reducing their confidence and engagement.",
                  },
                  {
                    title: "Lack of Real-Time Collaboration",
                    description:
                      "Builders, manufacturers, and engineers often work in silos, which slows down project delivery and increases errors.",
                  },
                ].map((pain, index) => (
                  <div
                    key={index}
                    className="bg-red-50 border border-red-200 rounded-lg p-6"
                  >
                    <h4 className="font-bold text-gray-900 mb-3">
                      {pain.title}
                    </h4>
                    <p className="text-red-700 text-sm leading-relaxed">
                      {pain.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            {/* Our Solution */}
            <div className="mb-20">
              <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-8 text-center">
                Our Solution
              </h3>
              <p className="text-lg text-gray-600 mb-12 text-center max-w-3xl mx-auto">
                We built the 3D Carport and Shed Designer Software to solve
                these challenges.
              </p>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[
                  "Automates complex engineering in real-time",
                  "Combines design, sales, and engineering in one seamless tool",
                  "Generates instant quotes and structural drawings",
                  "Allows customers to customize and visualize their building live",
                  "Keeps manufacturers, sellers, engineers, and builders fully connected",
                ].map((solution, index) => (
                  <div
                    key={index}
                    className="bg-green-50 border border-green-200 rounded-lg p-6"
                  >
                    <div className="flex items-start">
                      <CheckCircle className="h-6 w-6 text-green-600 mr-3 mt-1 flex-shrink-0" />
                      <p className="text-green-800 font-medium">{solution}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Who We Help */}
            <div className="mb-20">
              <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-12 text-center">
                Who We Help
              </h3>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {[
                  {
                    title: "Manufacturers",
                    description:
                      "Improve production speed, consistency, and material efficiency.",
                    icon: <Settings className="h-8 w-8 text-brand-red" />,
                  },
                  {
                    title: "Sellers",
                    description:
                      "Grow sales through instant quoting, customer-driven designs, and reduced lead times.",
                    icon: <TrendingUp className="h-8 w-8 text-brand-red" />,
                  },
                  {
                    title: "Builders",
                    description:
                      "Streamline site visits, speed up quoting, reduce design errors, and impress customers with live visualizations.",
                    icon: <Wrench className="h-8 w-8 text-brand-red" />,
                  },
                  {
                    title: "Engineers & Erectors",
                    description:
                      "Simplify the design, approval, and installation process with real-time engineering support.",
                    icon: <Calculator className="h-8 w-8 text-brand-red" />,
                  },
                  {
                    title: "Consumers",
                    description:
                      "Easily purchase affordable, customizable, and beautiful steel buildings.",
                    icon: <Users className="h-8 w-8 text-brand-red" />,
                  },
                ].map((audience, index) => (
                  <div
                    key={index}
                    className="text-center p-6 bg-gray-50 rounded-lg hover:shadow-md transition-shadow"
                  >
                    <div className="flex justify-center mb-4">
                      {audience.icon}
                    </div>
                    <h4 className="font-bold text-gray-900 mb-3 text-lg">
                      {audience.title}
                    </h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {audience.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            {/* Our Advantage */}
            <div className="mb-20">
              <div className="text-center bg-gradient-to-r from-brand-red to-brand-red-dark rounded-2xl p-8 text-white">
                <h3 className="text-2xl lg:text-3xl font-bold mb-4">
                  Our Advantage
                </h3>
                <p className="text-xl opacity-90 max-w-4xl mx-auto">
                  We combine BIM (Building Information Modeling) with live
                  engineering to make cold-formed buildings faster, smarter, and
                  more customer-friendly.
                </p>
              </div>
            </div>

            {/* Key Use Cases */}
            <div className="mb-20">
              <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-12 text-center">
                Key Use Cases
              </h3>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[
                  {
                    title: "Website Lead Funnel",
                    description:
                      "Customers visit a builder's website, design a carport, and submit an enquiry.",
                    icon: <Globe className="h-8 w-8 text-brand-red" />,
                  },
                  {
                    title: "Site Visit Support Tool",
                    description:
                      "Builders can load a customer's design on an iPad during site visits.",
                    icon: <FileText className="h-8 w-8 text-brand-red" />,
                  },
                  {
                    title: "Lead Qualification Automation",
                    description:
                      "Non-serious enquiries filter themselves out before reaching the sales team.",
                    icon: <CheckCircle className="h-8 w-8 text-brand-red" />,
                  },
                  {
                    title: "Walk-in Kiosk in Display Yard",
                    description:
                      "Customers can explore designs on an in-yard tablet before speaking to staff.",
                    icon: <Building className="h-8 w-8 text-brand-red" />,
                  },
                  {
                    title: "Remote Quoting",
                    description:
                      "Builders can receive customer designs remotely and prepare quotes without long phone calls.",
                    icon: <DollarSign className="h-8 w-8 text-brand-red" />,
                  },
                  {
                    title: "Sales Team Tool",
                    description:
                      "Distributors or sales reps can use the tool to guide customers through designs in person.",
                    icon: <Users className="h-8 w-8 text-brand-red" />,
                  },
                ].map((useCase, index) => (
                  <div
                    key={index}
                    className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-center mb-4">
                      <div className="p-2 bg-brand-red/10 rounded-lg mr-3">
                        {useCase.icon}
                      </div>
                      <h4 className="font-bold text-gray-900">
                        {useCase.title}
                      </h4>
                    </div>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {useCase.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Online Designer Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                  Online 3D Designer
                </h2>
                <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                  The 3D Designer software allows you to design, quote,
                  engineer, and order Cold Formed buildings from a single
                  platform.
                </p>
                <ul className="space-y-4 mb-8">
                  {[
                    "Anyone on the internet can access our free online designer tool",
                    "Embed this designer on your website for customer convenience",
                    "Customers can design and request quotes at their convenience",
                    "Complete, out-of-the-box solution for selling Cold Formed buildings",
                    "Fast, flexible, and easy to use platform",
                  ].map((item, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-brand-red mr-3 mt-1 flex-shrink-0" />
                      <span className="text-gray-700">{item}</span>
                    </li>
                  ))}
                </ul>
                <Button
                  className="bg-brand-red hover:bg-brand-red-dark text-white"
                  size="lg"
                >
                  Try the Online Designer
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </div>
              <div className="relative">
                <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-8">
                  <div className="aspect-video bg-white rounded-lg shadow-md flex items-center justify-center">
                    <div className="text-center">
                      <Code className="h-16 w-16 text-brand-red mx-auto mb-4" />
                      <h4 className="font-bold text-gray-900 mb-2">
                        3D Designer Interface
                      </h4>
                      <p className="text-gray-600">
                        Design, Quote, Engineer & Order
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        {/* CTA Section */}
        <section className="py-20 bg-gray-900 text-white relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-brand-red/20 to-transparent"></div>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative text-center">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">
              Ready to Get Started?
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
              Join thousands of manufacturers, sellers, engineers, and builders
              who trust our 3D Designer software for their COLD FORMED steel
              building projects.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                className="bg-brand-red hover:bg-brand-red-dark text-white"
                size="lg"
                onClick={() => navigate("/contact")}
              >
                Start Free Trial
              </Button>
            </div>
          </div>
        </section>
      </div>
      <Footer />
    </>
  );
};

export default Index;
