import { Badge } from "@/components/ui/badge";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import {
  Shield,
  Eye,
  Lock,
  Users,
  FileText,
  Calendar,
  AlertTriangle,
  CheckCircle,
} from "lucide-react";

const Privacy = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <Badge className="mb-6 bg-brand-red/20 text-brand-red border-brand-red/30 backdrop-blur-sm">
              Privacy Policy
            </Badge>
            <h1 className="text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight">
              Your Privacy is Our
              <span className="text-brand-red block"> Priority</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto mb-8 leading-relaxed">
              We are committed to protecting your personal information and
              ensuring transparency in how we collect, use, and safeguard your
              data.
            </p>
            <div className="flex items-center justify-center gap-8 text-sm text-gray-400">
              <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full">
                <Calendar className="h-4 w-4" />
                <span>Last updated: March 15, 2025</span>
              </div>
              <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full">
                <FileText className="h-4 w-4" />
                <span>Version 2.1</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Privacy Policy Content */}
      <section className="py-20 bg-white">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-16">
            {/* Introduction */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-brand-red/20 to-brand-red/10 rounded-xl flex items-center justify-center">
                  <Shield className="h-6 w-6 text-brand-red" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">
                  Introduction
                </h2>
              </div>
              <p className="text-gray-600 leading-relaxed text-lg">
                This Privacy Policy explains how we collect, use, store, and
                share personal information when you use our 3D visualisation
                platform and related features, including the "Get a Quote" form.
                By using our website and services, you consent to the handling
                of your personal information as described here.
              </p>
            </div>

            {/* Information We Collect */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-brand-red/20 to-brand-red/10 rounded-xl flex items-center justify-center">
                  <Eye className="h-6 w-6 text-brand-red" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">
                  Information We Collect
                </h2>
              </div>

              <p className="text-gray-600 mb-6 text-lg">
                We may collect the following personal data:
              </p>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                  <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                    <div className="w-2 h-2 bg-brand-red rounded-full"></div>
                    Form Submissions
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Name, email address, phone number, building preferences,
                    comments
                  </p>
                </div>
                <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                  <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                    <div className="w-2 h-2 bg-brand-red rounded-full"></div>
                    Technical Data
                  </h3>
                  <p className="text-gray-600 text-sm">
                    IP address, browser type, device type, referring page
                  </p>
                </div>
                <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                  <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                    <div className="w-2 h-2 bg-brand-red rounded-full"></div>
                    Usage Data
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Interaction with the tool (e.g. button clicks, session
                    times)
                  </p>
                </div>
                <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                  <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                    <div className="w-2 h-2 bg-brand-red rounded-full"></div>
                    Billing Information
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Subscription or billing information (for software users
                    only)
                  </p>
                </div>
              </div>
            </div>

            {/* How We Use Your Information */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-brand-red/20 to-brand-red/10 rounded-xl flex items-center justify-center">
                  <Users className="h-6 w-6 text-brand-red" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">
                  How We Use Your Information
                </h2>
              </div>

              <div className="space-y-4">
                {[
                  "To operate and improve the 3D visualisation service",
                  "To forward quote requests to independent third-party businesses",
                  "To respond to support queries or contact requests",
                  "For analytics, diagnostics, and service enhancement",
                  "To comply with legal obligations",
                ].map((item, index) => (
                  <div
                    key={index}
                    className="flex items-start gap-4 p-4 bg-white rounded-xl border border-gray-200 shadow-sm"
                  >
                    <div className="w-6 h-6 bg-brand-red/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-brand-red font-semibold text-sm">
                        {index + 1}
                      </span>
                    </div>
                    <p className="text-gray-700">{item}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Who We Share Your Data With */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-brand-red/20 to-brand-red/10 rounded-xl flex items-center justify-center">
                  <Lock className="h-6 w-6 text-brand-red" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">
                  Who We Share Your Data With
                </h2>
              </div>

              <p className="text-gray-600 mb-6 text-lg">
                We may share personal information with:
              </p>
              <div className="space-y-4 mb-6">
                <div className="flex items-center gap-3 p-4 bg-white rounded-xl border border-gray-200 shadow-sm">
                  <div className="w-3 h-3 bg-brand-red rounded-full"></div>
                  <p className="text-gray-700">
                    Businesses who receive enquiries from the "Get a Quote" form
                  </p>
                </div>
                <div className="p-4 bg-white rounded-xl border border-gray-200 shadow-sm">
                  <p className="text-gray-700 mb-3">
                    Third-party providers that support our infrastructure, such
                    as:
                  </p>
                  <div className="grid md:grid-cols-3 gap-3">
                    <div className="bg-gray-50 rounded-lg p-3 text-center">
                      <p className="text-sm font-medium text-gray-700">
                        Cloud Hosting
                      </p>
                      <p className="text-xs text-gray-500">Backend</p>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-3 text-center">
                      <p className="text-sm font-medium text-gray-700">
                        Deployment System
                      </p>
                      <p className="text-xs text-gray-500">Backend</p>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-3 text-center">
                      <p className="text-sm font-medium text-gray-700">
                        Google Analytics
                      </p>
                      <p className="text-xs text-gray-500">Aggregated data</p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-green-50 border border-green-200 rounded-xl p-4 flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <p className="text-green-800 font-semibold">
                  We do not sell or rent your personal data.
                </p>
              </div>
            </div>

            {/* Data Storage and Security */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Data Storage and Security
              </h2>
              <div className="grid md:grid-cols-3 gap-6">
                <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <div className="w-6 h-6 bg-blue-600 rounded"></div>
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    Australian Servers
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Data securely stored on servers located in Australia (e.g.,
                    Google Cloud)
                  </p>
                </div>
                <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm text-center">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Lock className="h-6 w-6 text-green-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    HTTPS/SSL Encryption
                  </h3>
                  <p className="text-gray-600 text-sm">
                    All data transmission is encrypted via HTTPS/SSL
                  </p>
                </div>
                <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm text-center">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Shield className="h-6 w-6 text-purple-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    Access Control
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Access strictly limited to authorised individuals under
                    confidentiality obligations
                  </p>
                </div>
              </div>
            </div>

            {/* Lead Data and Business Interaction */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Lead Data and Business Interaction
              </h2>
              <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
                <h3 className="font-semibold text-blue-900 mb-4 flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  When you submit a "Get a Quote" request:
                </h3>
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-blue-800">
                      Your information is securely forwarded to a designated
                      business based on criteria (e.g., location, availability)
                    </p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-blue-800">
                      We are not responsible for how the proposed builder
                      contacts or uses your information once received
                    </p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-blue-800">
                      Builders are independently responsible for their own
                      privacy practices
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Your Rights */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Your Rights
              </h2>
              <p className="text-gray-600 mb-6 text-lg">
                You have the right to:
              </p>
              <div className="grid md:grid-cols-2 gap-4 mb-6">
                {[
                  "Access the personal data we hold about you",
                  "Request corrections or deletion",
                  "Withdraw consent to receive marketing or communications",
                  "Lodge a complaint with the Office of the Australian Information Commissioner (OAIC)",
                ].map((right, index) => (
                  <div
                    key={index}
                    className="flex items-center gap-3 p-4 bg-white rounded-xl border border-gray-200 shadow-sm"
                  >
                    <div className="w-8 h-8 bg-brand-red/10 rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-brand-red font-semibold text-sm">
                        {index + 1}
                      </span>
                    </div>
                    <p className="text-gray-700">{right}</p>
                  </div>
                ))}
              </div>
              <div className="bg-brand-red/5 border border-brand-red/20 rounded-xl p-4">
                <p className="text-gray-700">
                  To exercise your rights, contact:{" "}
                  <strong className="text-brand-red">
                    <EMAIL>
                  </strong>
                </p>
              </div>
            </div>

            {/* Cookies */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Cookies</h2>
              <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                <p className="text-gray-700 leading-relaxed">
                  We use cookies for functionality and performance purposes.
                  These may collect anonymised traffic data. You may disable
                  cookies via your browser settings, but this may affect your
                  experience.
                </p>
              </div>
            </div>

            {/* Breach Notification */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Breach Notification
              </h2>
              <div className="bg-red-50 border border-red-200 rounded-xl p-6">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="h-6 w-6 text-red-600 mt-1 flex-shrink-0" />
                  <p className="text-red-800">
                    In the event of a data breach involving your personal
                    information, we will notify affected individuals and the
                    OAIC within 48 hours where required by law.
                  </p>
                </div>
              </div>
            </div>

            {/* Changes to Policy */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Changes to This Privacy Policy
              </h2>
              <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                <p className="text-gray-700 leading-relaxed">
                  We may update this privacy policy from time to time to reflect
                  changes in our practices or applicable laws. We will notify
                  you of any material changes by posting the updated policy on
                  our website and updating the "Last Updated" date. We encourage
                  you to review this policy periodically.
                </p>
              </div>
            </div>

            {/* Contact Information */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Contact Us
              </h2>
              <p className="text-gray-600 mb-6 text-lg">
                If you have any questions about this privacy policy or our data
                practices, please contact us:
              </p>
              <div className="bg-gradient-to-br from-gray-900 to-gray-800 text-white rounded-xl p-8">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <p className="text-gray-300 mb-2">
                      <strong className="text-white">Email:</strong>{" "}
                      <EMAIL>
                    </p>
                    <p className="text-gray-300 mb-2">
                      <strong className="text-white">Phone:</strong> +1 (555)
                      123-4567
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-300 mb-2">
                      <strong className="text-white">Address:</strong> 123
                      Business Street, Suite 100, City, State 12345
                    </p>
                    <p className="text-gray-300">
                      <strong className="text-white">
                        Data Protection Officer:
                      </strong>{" "}
                      <EMAIL>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Privacy;
