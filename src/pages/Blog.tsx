import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import { Calendar, User, ArrowRight } from "lucide-react";

const Blog = () => {
  const blogPosts = [
    {
      title: "The Future of COLD FORMED Steel Buildings",
      excerpt:
        "Discover how COLD FORMED steel construction is revolutionizing the building industry with faster construction times and lower costs.",
      date: "March 15, 2025",
      author: "<PERSON>",
      category: "Industry Insights",
      readTime: "5 min read",
    },
    {
      title: "5 Benefits of Using 3D Designer Software",
      excerpt:
        "Learn how our 3D Designer software streamlines the building design process from initial concept to final construction.",
      date: "March 10, 2025",
      author: "<PERSON>",
      category: "Software Tips",
      readTime: "3 min read",
    },
    {
      title: "Building Code Compliance Made Easy",
      excerpt:
        "Understanding how our software automatically ensures your buildings meet local wind and snow load requirements.",
      date: "March 5, 2025",
      author: "<PERSON>",
      category: "Engineering",
      readTime: "7 min read",
    },
    {
      title: "Case Study: Manufacturer Increases Revenue by 40%",
      excerpt:
        "See how one manufacturer transformed their business using our 3D Designer platform to expand into complete building solutions.",
      date: "February 28, 2025",
      author: "Lisa Chen",
      category: "Case Studies",
      readTime: "6 min read",
    },
    {
      title: "Sustainable Building with Steel",
      excerpt:
        "Explore the environmental benefits of COLD FORMED steel buildings and their contribution to sustainable construction.",
      date: "February 20, 2025",
      author: "David Brown",
      category: "Sustainability",
      readTime: "4 min read",
    },
    {
      title: "Getting Started as a Building Dealer",
      excerpt:
        "A comprehensive guide for new dealers looking to enter the COLD FORMED steel building market.",
      date: "February 15, 2025",
      author: "Emily Davis",
      category: "Business",
      readTime: "8 min read",
    },
  ];

  return (
    <>
      <div className="min-h-screen bg-white">
        <Navigation />

        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <Badge className="mb-6 bg-brand-red/10 text-brand-red border-brand-red/20">
                XYZ Company Blog
              </Badge>
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                Industry Insights &
                <span className="text-brand-red"> Expert Knowledge</span>
              </h1>
              <p className="text-xl text-gray-600 max-w-4xl mx-auto mb-8">
                Stay up-to-date with the latest trends, tips, and insights in
                COLD FORMED steel building design, manufacturing, and
                construction.
              </p>
            </div>
          </div>
        </section>

        {/* Blog Posts Grid */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {blogPosts.map((post, index) => (
                <Card
                  key={index}
                  className="border-0 shadow-sm hover:shadow-md transition-shadow cursor-pointer group"
                >
                  <CardContent className="p-0">
                    <div className="aspect-video bg-gradient-to-r from-gray-100 to-gray-200 rounded-t-lg mb-6"></div>
                    <div className="p-6">
                      <div className="flex items-center gap-2 mb-4">
                        <Badge
                          variant="outline"
                          className="border-brand-red/20 text-brand-red"
                        >
                          {post.category}
                        </Badge>
                        <span className="text-sm text-gray-500">
                          {post.readTime}
                        </span>
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-brand-red transition-colors">
                        {post.title}
                      </h3>
                      <p className="text-gray-600 mb-4 line-clamp-3">
                        {post.excerpt}
                      </p>
                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            <span>{post.date}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <User className="h-4 w-4" />
                            <span>{post.author}</span>
                          </div>
                        </div>
                        <ArrowRight className="h-4 w-4 text-brand-red group-hover:translate-x-1 transition-transform" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Load More Button */}
            <div className="text-center mt-12">
              <button className="bg-brand-red hover:bg-brand-red-dark text-white px-8 py-3 rounded-lg font-medium transition-colors">
                Load More Articles
              </button>
            </div>
          </div>
        </section>

        {/* Newsletter Signup */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Stay Updated
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Subscribe to our newsletter for the latest insights and updates
              from the COLD FORMED steel building industry.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-red focus:border-transparent"
              />
              <button className="bg-brand-red hover:bg-brand-red-dark text-white px-6 py-3 rounded-lg font-medium transition-colors">
                Subscribe
              </button>
            </div>
          </div>
        </section>
      </div>
      <Footer />
    </>
  );
};

export default Blog;
