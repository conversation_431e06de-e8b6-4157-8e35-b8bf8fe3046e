import { <PERSON>, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import {
  Store,
  TrendingUp,
  Users,
  CheckCircle,
  Clock,
  FileText,
  Truck,
  Headphones,
  Target,
} from "lucide-react";

const Selling = () => {
  const navigate = useNavigate();

  return (
    <>
      <div className="min-h-screen bg-white">
        <Navigation />

        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <Badge className="mb-6 bg-brand-red/10 text-brand-red border-brand-red/20">
                For Sellers & Dealers
              </Badge>
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                Out-of-the-Box Solution for
                <span className="text-brand-red"> Selling Steel Buildings</span>
              </h1>
              <p className="text-xl text-gray-600 max-w-4xl mx-auto mb-8">
                Get everything you need to start selling COLD FORMED steel
                buildings today. From lead generation to delivery, we provide
                the complete sales solution.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  className="bg-brand-red hover:bg-brand-red-dark text-white"
                  size="lg"
                  onClick={() => navigate("/contact")}
                >
                  Start Selling Today
                </Button>
                <Button variant="outline" size="lg">
                  View Demo
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* What We Give You Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Complete Sales Solution
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Everything you need to grow your steel building sales business.
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-12 items-start">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-8">
                  We Give You:
                </h3>
                <div className="grid gap-6">
                  {[
                    {
                      icon: <Store className="h-4 w-4 text-brand-red" />,
                      title: "Out-of-the-Box Solution",
                    },
                    {
                      icon: <Users className="h-4 w-4 text-brand-red" />,
                      title: "Online Training Materials",
                    },
                    {
                      icon: <Target className="h-4 w-4 text-brand-red" />,
                      title: "3D Lead Generator",
                    },
                    {
                      icon: <TrendingUp className="h-4 w-4 text-brand-red" />,
                      title: "Online Tracking",
                    },
                    {
                      icon: <CheckCircle className="h-4 w-4 text-brand-red" />,
                      title: "Easy Ordering",
                    },
                    {
                      icon: <Clock className="h-4 w-4 text-brand-red" />,
                      title: "Fast Engineering",
                    },
                    {
                      icon: <FileText className="h-4 w-4 text-brand-red" />,
                      title: "Construction Documents",
                    },
                    {
                      icon: <Headphones className="h-4 w-4 text-brand-red" />,
                      title: "Industry Leading Support",
                    },
                  ].map((feature, index) => (
                    <Card
                      key={index}
                      className="flex items-center shadow-sm hover:shadow-md transition-all duration-300 border-none h-12"
                    >
                      <CardContent className="flex items-center w-full px-3 py-0">
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0 p-1 bg-brand-red/10 rounded">
                            {feature.icon}
                          </div>
                          <div>
                            <h4 className="font-medium text-sm text-gray-900">
                              {feature.title}
                            </h4>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
              <div className="relative">
                <div className="bg-gradient-to-br from-brand-red to-brand-red-dark rounded-2xl p-8 text-white">
                  <Store className="h-16 w-16 mb-6" />
                  <h4 className="text-2xl font-bold mb-4">
                    Sales Success Metrics
                  </h4>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span>Lead Conversion Rate</span>
                      <span className="font-bold">35%+</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Average Sale Value</span>
                      <span className="font-bold">$25K</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Time to Quote</span>
                      <span className="font-bold">&lt; 1 Hour</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Customer Satisfaction</span>
                      <span className="font-bold">97%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gray-900 text-white relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-brand-red/20 to-transparent"></div>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative text-center">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">
              Ready to Start Selling Steel Buildings?
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
              Join successful dealers worldwide who are growing their business
              with our proven sales platform.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                className="bg-brand-red hover:bg-brand-red-dark text-white"
                size="lg"
                onClick={() => navigate("/contact")}
              >
                Become a Dealer
              </Button>
            </div>
          </div>
        </section>
      </div>
      <Footer />
    </>
  );
};

export default Selling;
