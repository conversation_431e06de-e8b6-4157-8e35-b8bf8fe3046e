import { Badge } from "@/components/ui/badge";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import {
  FileText,
  Calendar,
  Shield,
  AlertTriangle,
  CheckCircle,
  <PERSON>ie,
  Settings,
  Eye,
  Lock,
  ExternalLink,
} from "lucide-react";

const Cookies = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <Badge className="mb-6 bg-brand-red/20 text-brand-red border-brand-red/30 backdrop-blur-sm">
              Cookie Policy
            </Badge>
            <h1 className="text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight">
              Cookie
              <span className="text-brand-red block"> Policy</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto mb-8 leading-relaxed">
              Learn how we use cookies and similar technologies to enhance your
              experience on our 3D visualisation platform.
            </p>
            <div className="flex items-center justify-center gap-8 text-sm text-gray-400">
              <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full">
                <Calendar className="h-4 w-4" />
                <span>Last updated: March 15, 2025</span>
              </div>
              <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full">
                <FileText className="h-4 w-4" />
                <span>Version 1.0</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Cookie Policy Content */}
      <section className="py-20 bg-white">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-16">
            {/* About This Policy */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-brand-red/20 to-brand-red/10 rounded-xl flex items-center justify-center">
                  <FileText className="h-6 w-6 text-brand-red" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">
                  1. About This Policy
                </h2>
              </div>
              <p className="text-gray-600 leading-relaxed text-lg">
                This Cookie Policy explains how [Your Business Name] ("we",
                "us", or "our") uses cookies and similar technologies on our 3D
                visualisation platform and associated websites (the "Service").
                By continuing to use our Service, you consent to the use of
                cookies as described in this policy.
              </p>
            </div>

            {/* What Are Cookies */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-brand-red/20 to-brand-red/10 rounded-xl flex items-center justify-center">
                  <Cookie className="h-6 w-6 text-brand-red" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">
                  2. What Are Cookies?
                </h2>
              </div>
              <p className="text-gray-600 leading-relaxed text-lg">
                Cookies are small data files stored on your browser or device
                when you visit a website. They help improve your experience by
                remembering preferences, enabling core functionality, and
                collecting information for analytics.
              </p>
            </div>

            {/* Types of Cookies We Use */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                3. Types of Cookies We Use
              </h2>
              <div className="space-y-6">
                <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                  <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <Shield className="h-5 w-5 text-green-600" />
                    Strictly Necessary
                  </h3>
                  <p className="text-gray-600">
                    Enables secure login, form submissions, and core iframe
                    functionality.
                  </p>
                </div>

                <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                  <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <Eye className="h-5 w-5 text-blue-600" />
                    Performance & Analytics
                  </h3>
                  <p className="text-gray-600">
                    Helps us understand how users interact with the tool (e.g.,
                    session duration, button clicks) via anonymised data,
                    typically using Google Analytics.
                  </p>
                </div>

                <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                  <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <Settings className="h-5 w-5 text-purple-600" />
                    Preference Cookies
                  </h3>
                  <p className="text-gray-600">
                    May remember previous selections (e.g., preferred building
                    type) to simplify repeat visits.
                  </p>
                </div>

                <div className="bg-green-50 border border-green-200 rounded-xl p-6">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <p className="text-green-800 font-semibold">
                      We do not use cookies for advertising, behavioural
                      targeting, or cross-site tracking.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Third-Party Cookies */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                4. Third-Party Cookies
              </h2>
              <div className="space-y-4">
                <p className="text-gray-600 text-lg">
                  We may allow third-party services (e.g., Google Analytics,
                  hosting providers) to place cookies for diagnostic and usage
                  tracking purposes. These cookies are subject to the respective
                  providers' privacy policies.
                </p>
                <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
                  <p className="text-blue-800 mb-3">
                    You can view Google's policy at:
                  </p>
                  <a
                    href="https://policies.google.com/technologies/cookies"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 font-medium flex items-center gap-2"
                  >
                    https://policies.google.com/technologies/cookies
                    <ExternalLink className="h-4 w-4" />
                  </a>
                </div>
              </div>
            </div>

            {/* Managing Cookies */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-brand-red/20 to-brand-red/10 rounded-xl flex items-center justify-center">
                  <Settings className="h-6 w-6 text-brand-red" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">
                  5. Managing Cookies
                </h2>
              </div>

              <div className="space-y-6">
                <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                  <p className="text-gray-600 mb-4">
                    Most web browsers accept cookies by default, but you can
                    change your browser settings to remove or block cookies.
                    However, please note that disabling certain cookies may
                    impact the functionality of the Service, including form
                    submissions or rendering performance.
                  </p>
                  <h3 className="font-semibold text-gray-900 mb-4">
                    To manage cookies in common browsers:
                  </h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-2">Chrome</h4>
                      <p className="text-sm text-gray-600">
                        Settings &gt; Privacy and security &gt; Cookies
                      </p>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-2">Safari</h4>
                      <p className="text-sm text-gray-600">
                        Preferences &gt; Privacy
                      </p>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-2">
                        Firefox
                      </h4>
                      <p className="text-sm text-gray-600">
                        Options &gt; Privacy & Security
                      </p>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-2">Edge</h4>
                      <p className="text-sm text-gray-600">
                        Settings &gt; Site Permissions &gt; Cookies and site
                        data
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Cookies and Embedded Use */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                6. Cookies and Embedded Use (Iframe)
              </h2>
              <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-6">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="h-6 w-6 text-yellow-600 mt-1 flex-shrink-0" />
                  <p className="text-yellow-800">
                    When our Service is embedded on a third-party site via
                    iframe, cookies may still be set or read from the user's
                    browser. It is the responsibility of the embedding website
                    to inform their end users of this use as part of their
                    privacy and cookie disclosures, where required by law.
                  </p>
                </div>
              </div>
            </div>

            {/* Updates to Policy */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                7. Updates to This Policy
              </h2>
              <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                <p className="text-gray-700 leading-relaxed">
                  We may update this Cookie Policy from time to time to reflect
                  changes in technology, legal requirements, or service
                  enhancements. Updates will be posted on this page with a
                  revised effective date.
                </p>
              </div>
            </div>

            {/* Contact Information */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                8. Contact Us
              </h2>
              <div className="bg-gradient-to-br from-gray-900 to-gray-800 text-white rounded-xl p-8">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <p className="text-gray-300 mb-2">
                      <strong className="text-white">Email:</strong>{" "}
                      <EMAIL>
                    </p>
                    <p className="text-gray-300 mb-2">
                      <strong className="text-white">Phone:</strong> +1 (555)
                      123-4567
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-300 mb-2">
                      <strong className="text-white">Address:</strong> 123
                      Business Street, Suite 100, City, State 12345
                    </p>
                    <p className="text-gray-300">
                      <strong className="text-white">
                        Data Protection Officer:
                      </strong>{" "}
                      <EMAIL>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Cookies;
