import { Badge } from "@/components/ui/badge";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import {
  FileText,
  Calendar,
  Shield,
  AlertTriangle,
  CheckCircle,
  Users,
  Lock,
  Scale,
  Info,
} from "lucide-react";

const Terms = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <Badge className="mb-6 bg-brand-red/20 text-brand-red border-brand-red/30 backdrop-blur-sm">
              Terms of Service
            </Badge>
            <h1 className="text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight">
              Terms of
              <span className="text-brand-red block"> Service</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto mb-8 leading-relaxed">
              Please read these terms carefully before using our 3D
              visualisation platform and related services.
            </p>
            <div className="flex items-center justify-center gap-8 text-sm text-gray-400">
              <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full">
                <Calendar className="h-4 w-4" />
                <span>Last updated: March 15, 2025</span>
              </div>
              <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full">
                <FileText className="h-4 w-4" />
                <span>Version 1.0</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Terms Content */}
      <section className="py-20 bg-white">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-16">
            {/* About This Service */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-brand-red/20 to-brand-red/10 rounded-xl flex items-center justify-center">
                  <Info className="h-6 w-6 text-brand-red" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">
                  1. About This Service
                </h2>
              </div>
              <div className="space-y-4">
                <p className="text-gray-600 leading-relaxed text-lg">
                  This web-based 3D visualisation tool provides conceptual
                  representations of cold-form steel buildings (e.g. carports,
                  sheds) for informational and visualisation purposes only. It
                  is not an engineering tool, and the results must not be relied
                  upon for compliance, certification, or structural accuracy.
                </p>
                <p className="text-gray-600 leading-relaxed text-lg">
                  A "Get a Quote" function may be provided for your convenience,
                  allowing you to submit basic contact and design information to
                  a participating builder. Submitting a quote request does not
                  constitute a formal agreement or guarantee of service.
                </p>
              </div>
            </div>

            {/* Acceptance of Terms */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-brand-red/20 to-brand-red/10 rounded-xl flex items-center justify-center">
                  <FileText className="h-6 w-6 text-brand-red" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">
                  2. Acceptance of Terms
                </h2>
              </div>
              <div className="space-y-4">
                <p className="text-gray-600 leading-relaxed text-lg">
                  By accessing or using this service, including submitting a
                  quote request, you acknowledge that you have read, understood,
                  and agree to be bound by these Terms of Use. If you do not
                  agree, you must not use this service.
                </p>
                <p className="text-gray-600 leading-relaxed text-lg">
                  We reserve the right to modify these Terms at any time.
                  Continued use of the service following any such changes
                  constitutes acceptance of the updated terms.
                </p>
              </div>
            </div>

            {/* Service Limitations & Disclaimer */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-brand-red/20 to-brand-red/10 rounded-xl flex items-center justify-center">
                  <AlertTriangle className="h-6 w-6 text-brand-red" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">
                  3. Service Limitations & Disclaimer
                </h2>
              </div>
              <div className="space-y-4">
                <div className="bg-red-50 border border-red-200 rounded-xl p-6">
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0"></div>
                      <p className="text-red-800">
                        The tool is for visualisation only and does not replace
                        the need for professional advice or certified
                        engineering documentation.
                      </p>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0"></div>
                      <p className="text-red-800">
                        The results, previews, and quote estimates are
                        indicative only and may not reflect final pricing,
                        construction feasibility, or compliance.
                      </p>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0"></div>
                      <p className="text-red-800">
                        Quote submissions are routed to third-party builders who
                        are independent of the platform. We do not verify their
                        qualifications, licensing, or responses.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* User Conduct */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-brand-red/20 to-brand-red/10 rounded-xl flex items-center justify-center">
                  <Users className="h-6 w-6 text-brand-red" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">
                  4. User Conduct
                </h2>
              </div>

              <p className="text-gray-600 mb-6 text-lg">
                By using this service, you agree:
              </p>
              <div className="space-y-4">
                {[
                  "Not to misuse or abuse the system, interface, quote form, or design outputs.",
                  "Not to submit false, misleading, or malicious information.",
                  "Not to attempt to decompile, reverse engineer, or gain unauthorised access to the tool or its components.",
                ].map((item, index) => (
                  <div
                    key={index}
                    className="flex items-start gap-4 p-4 bg-white rounded-xl border border-gray-200 shadow-sm"
                  >
                    <div className="w-6 h-6 bg-brand-red/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-brand-red font-semibold text-sm">
                        {index + 1}
                      </span>
                    </div>
                    <p className="text-gray-700">{item}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Intellectual Property */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-brand-red/20 to-brand-red/10 rounded-xl flex items-center justify-center">
                  <Lock className="h-6 w-6 text-brand-red" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">
                  5. Intellectual Property
                </h2>
              </div>

              <div className="space-y-4">
                <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                  <p className="text-gray-700 leading-relaxed">
                    All software, code, designs, and intellectual property
                    rights in this service are owned by [Your Legal Name / Sole
                    Trader].
                  </p>
                </div>
                <div className="bg-red-50 border border-red-200 rounded-xl p-6">
                  <p className="text-red-800 font-semibold">
                    You may not copy, modify, distribute, or reproduce any part
                    of the service without prior written consent.
                  </p>
                </div>
              </div>
            </div>

            {/* Liability & Warranty Disclaimer */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                6. Liability & Warranty Disclaimer
              </h2>
              <div className="bg-red-50 border border-red-200 rounded-xl p-6">
                <p className="text-red-800 font-semibold mb-4">
                  To the fullest extent permitted by law:
                </p>
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-red-800">
                      This tool and any design outputs are provided "as is"
                      without warranties of any kind.
                    </p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-red-800">
                      We are not liable for any construction decisions, project
                      delays, costs, damages, or disputes arising from use of
                      the tool or from quote submissions.
                    </p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-red-800">
                      We are not a party to any contract, quote, or engagement
                      between you and any third-party builder.
                    </p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-red-800">
                      Builders are solely responsible for reviewing
                      user-submitted leads and ensuring their own compliance and
                      due diligence.
                    </p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-red-800">
                      No guarantee is made that the service will meet your
                      specific needs, function without error, or remain
                      uninterrupted.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Termination of Access */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                7. Termination of Access
              </h2>
              <div className="space-y-4">
                <p className="text-gray-600 text-lg">
                  We reserve the right to terminate or restrict your access to
                  the service if you breach these Terms or engage in any form of
                  misuse, including but not limited to:
                </p>
                <div className="space-y-3">
                  <div className="flex items-start gap-3 p-4 bg-white rounded-xl border border-gray-200 shadow-sm">
                    <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-gray-700">
                      Tampering with or circumventing technical protections
                    </p>
                  </div>
                  <div className="flex items-start gap-3 p-4 bg-white rounded-xl border border-gray-200 shadow-sm">
                    <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-gray-700">
                      Submitting spam, bots, or automated requests
                    </p>
                  </div>
                  <div className="flex items-start gap-3 p-4 bg-white rounded-xl border border-gray-200 shadow-sm">
                    <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-gray-700">
                      Attempting to damage or overload the service
                      infrastructure
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Governing Law */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-brand-red/20 to-brand-red/10 rounded-xl flex items-center justify-center">
                  <Scale className="h-6 w-6 text-brand-red" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">
                  8. Governing Law
                </h2>
              </div>
              <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                <p className="text-gray-700 leading-relaxed">
                  These Terms of Use are governed by and interpreted in
                  accordance with the laws of New South Wales, Australia. You
                  agree to submit to the exclusive jurisdiction of the courts of
                  New South Wales for any legal matter arising under or related
                  to these Terms.
                </p>
              </div>
            </div>

            {/* Contact Information */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Contact</h2>
              <div className="bg-gradient-to-br from-gray-900 to-gray-800 text-white rounded-xl p-8">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <p className="text-gray-300 mb-2">
                      <strong className="text-white">Email:</strong>{" "}
                      <EMAIL>
                    </p>
                    <p className="text-gray-300 mb-2">
                      <strong className="text-white">Phone:</strong> +1 (555)
                      123-4567
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-300 mb-2">
                      <strong className="text-white">Address:</strong> 123
                      Business Street, Suite 100, City, State 12345
                    </p>
                    <p className="text-gray-300">
                      <strong className="text-white">Legal Department:</strong>{" "}
                      <EMAIL>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Terms;
