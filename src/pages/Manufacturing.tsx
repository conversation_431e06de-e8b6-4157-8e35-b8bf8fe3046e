import { <PERSON>, useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import {
  Factory,
  Settings,
  TrendingUp,
  Users,
  CheckCircle,
  Zap,
  BarChart3,
  Network,
  FileText,
  Headphones,
} from "lucide-react";

const Manufacturing = () => {
  const navigate = useNavigate();

  return (
    <>
      <div className="min-h-screen bg-white">
        <Navigation />

        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <Badge className="mb-6 bg-brand-red/10 text-brand-red border-brand-red/20">
                For Manufacturers
              </Badge>
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                From Producing Components to
                <span className="text-brand-red"> Producing Buildings</span>
              </h1>
              <p className="text-xl text-gray-600 max-w-4xl mx-auto mb-8">
                You spent good money on your roll-formers. When your machines
                sit idle, you're losing income. With 3D Designer, your customers
                can design and order COLD FORMED steel buildings using
                components from your catalog.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  className="bg-brand-red hover:bg-brand-red-dark text-white"
                  size="lg"
                  onClick={() =>
                    window.open("http://localhost:8084/", "_blank")
                  }
                >
                  Try Free 3D Designer
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => navigate("/contact")}
                >
                  Get Started Today
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Maximize Your Production Efficiency
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                You select the components. We customize the program to your
                business, creating a turnkey building solution.
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-12 items-start mb-20">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-8">
                  What We Give You:
                </h3>
                <div className="grid gap-2">
                  {[
                    {
                      icon: <Settings className="h-4 w-4 text-brand-red" />,
                      title: "Customized 3D Designer Software",
                    },
                    {
                      icon: <Users className="h-4 w-4 text-brand-red" />,
                      title: "Online Training Tools",
                    },
                    {
                      icon: <TrendingUp className="h-4 w-4 text-brand-red" />,
                      title: "3D Online Lead Generator",
                    },
                    {
                      icon: <BarChart3 className="h-4 w-4 text-brand-red" />,
                      title: "Online Dashboard",
                    },
                    {
                      icon: <Network className="h-4 w-4 text-brand-red" />,
                      title: "Scalable Network",
                    },
                    {
                      icon: <Zap className="h-4 w-4 text-brand-red" />,
                      title: "ERP Integration",
                    },
                    {
                      icon: <FileText className="h-4 w-4 text-brand-red" />,
                      title: "Full Documentation",
                    },
                    {
                      icon: <Headphones className="h-4 w-4 text-brand-red" />,
                      title: "Industry-Leading Support",
                    },
                  ].map((feature, index) => (
                    <Card
                      key={index}
                      className="flex items-center shadow-sm hover:shadow-md transition-all duration-300 border-none h-12"
                    >
                      <CardContent className="flex items-center w-full px-3 py-0">
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0 p-1 bg-brand-red/10 rounded">
                            {feature.icon}
                          </div>
                          <div>
                            <h4 className="font-medium text-sm text-gray-900">
                              {feature.title}
                            </h4>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
              <div className="relative">
                <div className="bg-gradient-to-br from-brand-red to-brand-red-dark rounded-2xl p-8 text-white">
                  <Factory className="h-16 w-16 mb-6" />
                  <h4 className="text-2xl font-bold mb-4">
                    Manufacturing Success
                  </h4>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span>Increased Production</span>
                      <span className="font-bold">+40%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Reduced Idle Time</span>
                      <span className="font-bold">-60%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>New Revenue Streams</span>
                      <span className="font-bold">+3</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Customer Satisfaction</span>
                      <span className="font-bold">98%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Features Grid */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Complete Manufacturing Solution
              </h2>
              <p className="text-xl text-gray-600">
                Everything you need to transform your component business into a
                complete building solution.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  icon: <Settings className="h-12 w-12 text-brand-red" />,
                  title: "Custom Configuration",
                  description:
                    "Tailor the software to match your exact component specifications and business processes.",
                },
                {
                  icon: <TrendingUp className="h-12 w-12 text-brand-red" />,
                  title: "Increased Revenue",
                  description:
                    "Turn component sales into complete building solutions with higher margins.",
                },
                {
                  icon: <Users className="h-12 w-12 text-brand-red" />,
                  title: "Dealer Network",
                  description:
                    "Build and manage a network of authorized dealers to expand your market reach.",
                },
                {
                  icon: <Zap className="h-12 w-12 text-brand-red" />,
                  title: "Automated Ordering",
                  description:
                    "Seamless integration with your existing ERP and production systems.",
                },
                {
                  icon: <FileText className="h-12 w-12 text-brand-red" />,
                  title: "Engineering Docs",
                  description:
                    "Automatically generated engineering drawings and construction documentation.",
                },
                {
                  icon: <BarChart3 className="h-12 w-12 text-brand-red" />,
                  title: "Analytics Dashboard",
                  description:
                    "Real-time insights into sales performance, lead generation, and production planning.",
                },
              ].map((feature, index) => (
                <Card
                  key={index}
                  className="border-0 shadow-sm hover:shadow-md transition-shadow"
                >
                  <CardContent className="p-8 text-center">
                    <div className="mb-6 flex justify-center">
                      {feature.icon}
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-4">
                      {feature.title}
                    </h3>
                    <p className="text-gray-600">{feature.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gray-900 text-white relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-brand-red/20 to-transparent"></div>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative text-center">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">
              Ready to Transform Your Manufacturing Business?
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
              Join manufacturers worldwide who have increased their revenue and
              optimized their production with our 3D Designer software.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                className="bg-brand-red hover:bg-brand-red-dark text-white"
                size="lg"
                onClick={() => navigate("/contact")}
              >
                Contact Sales Team
              </Button>
            </div>
          </div>
        </section>
      </div>
      <Footer />
    </>
  );
};

export default Manufacturing;
