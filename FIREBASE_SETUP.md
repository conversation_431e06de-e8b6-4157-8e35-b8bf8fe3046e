# Firebase Setup Guide for Contact Form

This guide will help you set up Firebase Firestore and Cloud Functions for the contact form with email notifications.

## Prerequisites

1. A Firebase project
2. Node.js installed
3. Firebase CLI installed (`npm install -g firebase-tools`)

## Step 1: Install Dependencies

```bash
# Install Firebase SDK in your main project
npm install firebase

# Install Cloud Functions dependencies
cd functions
npm install
```

## Step 2: Firebase Project Setup

1. **Create a Firebase project** at [Firebase Console](https://console.firebase.google.com/)
2. **Enable Firestore Database**:

   - Go to Firestore Database in Firebase Console
   - Click "Create Database"
   - Choose "Start in test mode" for development
   - Select a location close to your users

3. **Enable Cloud Functions**:
   - Go to Functions in Firebase Console
   - Click "Get Started"
   - Choose Node.js 18
   - Select a location

## Step 3: Environment Configuration

Create a `.env` file in your project root:

```env
# Firebase Configuration
VITE_FIREBASE_API_KEY=your_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
```

## Step 4: Firebase Configuration

Update `firebase.json` to include functions:

```json
{
  "hosting": {
    "public": "dist",
    "ignore": ["firebase.json", "**/.*", "**/node_modules/**"],
    "rewrites": [
      {
        "source": "**",
        "destination": "/index.html"
      }
    ]
  },
  "functions": {
    "source": "functions"
  }
}
```

## Step 5: Email Configuration

### Option A: Gmail (Recommended for testing)

1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate an App Password**:

   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate a password for "Mail"

3. **Set Firebase Functions config**:

```bash
firebase functions:config:set email.user="<EMAIL>"
firebase functions:config:set email.password="your-app-password"
firebase functions:config:set email.admin="<EMAIL>"
```

### Option B: Custom SMTP Server

Update the transporter configuration in `functions/index.js`:

```javascript
const transporter = nodemailer.createTransporter({
  host: "your-smtp-host.com",
  port: 587,
  secure: false,
  auth: {
    user: functions.config().email.user,
    pass: functions.config().email.password,
  },
});
```

## Step 6: Deploy Cloud Functions

```bash
# Deploy functions
firebase deploy --only functions

# Or deploy everything
firebase deploy
```

## Step 7: Firestore Security Rules

Update your Firestore security rules in Firebase Console:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read/write access to contactSubmissions collection
    match /contactSubmissions/{document} {
      allow read, write: if true; // For development - restrict in production
    }
  }
}
```

## Step 8: Testing

1. **Test the contact form** on your website
2. **Check Firestore** for new documents in the `contactSubmissions` collection
3. **Check your email** for notifications
4. **Test Cloud Function** directly:
   ```bash
   # Test email function
   curl -X POST https://your-region-your-project.cloudfunctions.net/testEmail
   ```

## Production Considerations

### Security

1. **Update Firestore rules** to restrict access:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /contactSubmissions/{document} {
      allow read: if request.auth != null && request.auth.token.admin == true;
      allow write: if true; // Allow form submissions
    }
  }
}
```

2. **Enable Firebase Authentication** for admin access
3. **Set up proper CORS** for Cloud Functions

### Email Service

1. **Use a professional email service** like SendGrid, Mailgun, or AWS SES
2. **Set up email templates** for better branding
3. **Configure email verification** for customer emails

### Monitoring

1. **Set up Firebase Analytics** to track form submissions
2. **Configure error monitoring** with Firebase Crashlytics
3. **Set up email alerts** for failed submissions

## Troubleshooting

### Common Issues

1. **"Functions not found" error**:

   - Make sure you've deployed the functions
   - Check the function name matches exactly

2. **Email not sending**:

   - Verify email credentials in Firebase config
   - Check Gmail app password is correct
   - Review Cloud Function logs in Firebase Console

3. **Firestore permission errors**:

   - Check security rules
   - Verify API key is correct
   - Ensure Firestore is enabled

4. **CORS errors**:
   - Add your domain to authorized origins in Firebase Console
   - Check Cloud Function CORS configuration

### Debugging

1. **Check Cloud Function logs**:

```bash
firebase functions:log
```

2. **Test functions locally**:

```bash
firebase emulators:start
```

3. **View Firestore data**:
   - Go to Firebase Console → Firestore Database
   - Check the `contactSubmissions` collection

## File Structure

```
your-project/
├── src/
│   ├── lib/
│   │   └── firebase.ts          # Firebase configuration and functions
│   └── pages/
│       └── Contact.tsx          # Updated contact form
├── functions/
│   ├── index.js                 # Cloud Functions for email
│   └── package.json             # Functions dependencies
├── firebase.json               # Firebase configuration
├── .firebaserc                 # Firebase project settings
└── .env                        # Environment variables
```

## Support

If you encounter issues:

1. Check the Firebase Console for error logs
2. Review the Cloud Function logs
3. Verify all environment variables are set correctly
4. Ensure all dependencies are installed
