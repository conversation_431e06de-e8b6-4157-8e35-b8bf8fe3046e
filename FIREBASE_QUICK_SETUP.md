# Quick Firebase Setup Fix

## The Issue

The error "Function addDoc() called with invalid data. Unsupported field value: undefined" occurs because Firestore doesn't accept `undefined` values.

## ✅ What's Fixed

1. **Removed undefined values** from Firestore data
2. **Added proper error handling** in Cloud Functions
3. **Improved data validation** and formatting
4. **Added helper functions** for safe data processing

## 🚀 Quick Setup Steps

### 1. Install Dependencies (Already Done)

```bash
npm install firebase
cd functions && npm install
```

### 2. Create Environment File

Create a `.env` file in your project root:

```env
VITE_FIREBASE_API_KEY=your_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
```

### 3. Set Up Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project or select existing one
3. Enable Firestore Database (start in test mode)
4. Enable Cloud Functions
5. Get your project configuration

### 4. Configure Email (Optional for Testing)

```bash
firebase functions:config:set email.user="<EMAIL>"
firebase functions:config:set email.password="your-app-password"
firebase functions:config:set email.admin="<EMAIL>"
```

### 5. Deploy Functions

```bash
firebase deploy --only functions
```

## 🔧 Testing the Fix

### Test Environment Variables

Add this to your Contact page temporarily:

```javascript
import { testEnvironmentVariables } from "@/lib/firebase-test";

// Add this in useEffect
useEffect(() => {
  const envTest = testEnvironmentVariables();
  console.log("Environment test:", envTest);
}, []);
```

### Test Form Submission

1. Fill out the contact form
2. Submit and check browser console
3. Check Firestore for new document
4. Check email (if configured)

## 🐛 Common Issues & Solutions

### "Missing environment variables"

- Create `.env` file with all required variables
- Restart your development server

### "Firestore permission denied"

- Check Firestore rules in Firebase Console
- Use test mode for development

### "Email not sending"

- Check Firebase Functions logs
- Verify email configuration
- Check Gmail app password

### "Functions not found"

- Deploy functions: `firebase deploy --only functions`
- Check function name matches exactly

## 📊 What Data is Saved

Each submission now includes:

- ✅ Form data (name, email, phone, location, message)
- ✅ Location info (detected country, city, timezone)
- ✅ Analytics (IP, user agent, referrer, UTM params)
- ✅ Metadata (timestamp, status, email status)
- ✅ No undefined values (fixed!)

## 🎯 Next Steps

1. **Test the form** - Submit a test contact form
2. **Check Firestore** - Verify data is saved correctly
3. **Configure email** - Set up email notifications
4. **Deploy to production** - Update security rules

## 📞 Support

If you still encounter issues:

1. Check browser console for errors
2. Check Firebase Console logs
3. Verify all environment variables are set
4. Ensure Firebase project is properly configured
