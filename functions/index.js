const functions = require("firebase-functions");
const admin = require("firebase-admin");
const nodemailer = require("nodemailer");

// Initialize Firebase Admin
admin.initializeApp();

// Email configuration for Gmail
const transporter = nodemailer.createTransport({
  host: "smtp.gmail.com",
  port: 587,
  secure: false, // true for 465, false for other ports
  auth: {
    user: functions.config().email.user, // Gmail email address
    pass: functions.config().email.password, // Gmail app password
  },
});

// Cloud Function to send contact form email notifications
exports.sendContactEmail = functions.https.onCall(async (data, context) => {
  try {
    const { docId, formData, locationInfo } = data;

    // Validate required data
    if (!formData || !formData.name || !formData.email || !formData.message) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Missing required form data",
      );
    }

    // Format email data with safe defaults
    const emailData = {
      customerName: formData.name || "Unknown",
      customerEmail: formData.email,
      customerPhone: formData.phone || "Not provided",
      customerLocation: formatLocation(formData),
      detectedLocation: formatDetectedLocation(locationInfo),
      message: formData.message,
      timezone: locationInfo?.timezone || "Unknown",
      currency: locationInfo?.currency || "Unknown",
      timestamp: new Date().toLocaleString(),
      docId: docId || "Unknown",
    };

    // Send email to customer (confirmation)
    const customerEmailResult = await sendCustomerEmail(emailData);

    // Send email to admin (notification)
    const adminEmailResult = await sendAdminEmail(emailData);

    // Update Firestore document to mark emails as sent
    if (docId) {
      try {
        await admin
          .firestore()
          .collection("contactSubmissions")
          .doc(docId)
          .update({
            emailSent: true,
            emailSentAt: admin.firestore.FieldValue.serverTimestamp(),
            status: "processed",
          });
      } catch (updateError) {
        console.log("WARN: Failed to update Firestore document", {
          error: updateError.message,
          docId: docId,
          timestamp: new Date().toISOString(),
        });
        // Don't fail the entire function if update fails
      }
    }

    return {
      success: true,
      customerEmailSent: customerEmailResult.success,
      adminEmailSent: adminEmailResult.success,
    };
  } catch (error) {
    console.log("ERROR: Error sending contact emails", {
      error: error.message,
      stack: error.stack,
      docId: data.docId,
      timestamp: new Date().toISOString(),
    });

    // Update Firestore document to mark as failed
    if (data.docId) {
      try {
        await admin
          .firestore()
          .collection("contactSubmissions")
          .doc(data.docId)
          .update({
            status: "failed",
            error: error.message,
          });
      } catch (updateError) {
        console.log(
          "WARN: Failed to update Firestore document with error status",
          {
            error: updateError.message,
            originalError: error.message,
            docId: data.docId,
            timestamp: new Date().toISOString(),
          },
        );
      }
    }

    throw new functions.https.HttpsError(
      "internal",
      "Failed to send email notifications",
    );
  }
});

// Helper function to format customer location
function formatLocation(formData) {
  const parts = [];
  if (formData.city) parts.push(formData.city);
  if (formData.state) parts.push(formData.state);
  if (formData.country) parts.push(formData.country);
  return parts.length > 0 ? parts.join(", ") : "Not provided";
}

// Helper function to format detected location
function formatDetectedLocation(locationInfo) {
  if (!locationInfo || !locationInfo.detected) {
    return "Not detected";
  }
  const parts = [];
  if (locationInfo.city) parts.push(locationInfo.city);
  if (locationInfo.country) parts.push(locationInfo.country);
  return parts.length > 0
    ? parts.join(", ")
    : "Location detected but details unavailable";
}

// Function to send confirmation email to customer
async function sendCustomerEmail(emailData) {
  try {
    const customerEmailContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #dc2626; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0;">XYZ Company</h1>
          <p style="margin: 10px 0 0 0;">Thank you for contacting us!</p>
        </div>
        
        <div style="padding: 30px; background-color: #f9fafb;">
          <h2 style="color: #374151; margin-bottom: 20px;">Hello ${emailData.customerName},</h2>
          
          <p style="color: #6b7280; line-height: 1.6; margin-bottom: 20px;">
            Thank you for reaching out to XYZ Company. We have received your inquiry and our team will get back to you within 24 hours.
          </p>
          
          <div style="background-color: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #374151; margin-top: 0;">Your Message Details:</h3>
            <p style="color: #6b7280; margin: 5px 0;"><strong>Name:</strong> ${emailData.customerName}</p>
            <p style="color: #6b7280; margin: 5px 0;"><strong>Email:</strong> ${emailData.customerEmail}</p>
            <p style="color: #6b7280; margin: 5px 0;"><strong>Phone:</strong> ${emailData.customerPhone}</p>
            <p style="color: #6b7280; margin: 5px 0;"><strong>Location:</strong> ${emailData.customerLocation}</p>
            <p style="color: #6b7280; margin: 5px 0;"><strong>Message:</strong></p>
            <div style="background-color: #f3f4f6; padding: 15px; border-radius: 4px; margin-top: 10px;">
              <p style="color: #374151; margin: 0; white-space: pre-wrap;">${emailData.message}</p>
            </div>
          </div>
          
          <p style="color: #6b7280; line-height: 1.6;">
            If you have any urgent questions, please don't hesitate to call us at +****************.
          </p>
          
          <p style="color: #6b7280; line-height: 1.6;">
            Best regards,<br>
            The XYZ Company Team
          </p>
        </div>
        
        <div style="background-color: #374151; color: white; padding: 20px; text-align: center; font-size: 14px;">
          <p style="margin: 0;">© 2024 XYZ Company. All rights reserved.</p>
          <p style="margin: 5px 0;">123 Innovation Drive, Steel City, SC 12345</p>
        </div>
      </div>
    `;

    const mailOptions = {
      from: functions.config().email.user, // Gmail email address
      to: emailData.customerEmail,
      subject: "Thank you for contacting XYZ Company",
      html: customerEmailContent,
    };

    await transporter.sendMail(mailOptions);

    return { success: true };
  } catch (error) {
    console.log("ERROR: Error sending customer email", {
      error: error.message,
      stack: error.stack,
      customerEmail: emailData.customerEmail,
      timestamp: new Date().toISOString(),
    });
    return { success: false, error: error.message };
  }
}

// Function to send notification email to admin
async function sendAdminEmail(emailData) {
  try {
    const adminEmailContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #dc2626; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0;">New Contact Form Submission</h1>
          <p style="margin: 10px 0 0 0;">XYZ Company Website</p>
        </div>
        
        <div style="padding: 30px; background-color: #f9fafb;">
          <h2 style="color: #374151; margin-bottom: 20px;">New Inquiry Received</h2>
          
          <div style="background-color: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #374151; margin-top: 0;">Customer Information:</h3>
            <p style="color: #6b7280; margin: 5px 0;"><strong>Name:</strong> ${emailData.customerName}</p>
            <p style="color: #6b7280; margin: 5px 0;"><strong>Email:</strong> ${emailData.customerEmail}</p>
            <p style="color: #6b7280; margin: 5px 0;"><strong>Phone:</strong> ${emailData.customerPhone}</p>
            <p style="color: #6b7280; margin: 5px 0;"><strong>Location:</strong> ${emailData.customerLocation}</p>
            <p style="color: #6b7280; margin: 5px 0;"><strong>Detected Location:</strong> ${emailData.detectedLocation}</p>
            <p style="color: #6b7280; margin: 5px 0;"><strong>Timezone:</strong> ${emailData.timezone}</p>
            <p style="color: #6b7280; margin: 5px 0;"><strong>Currency:</strong> ${emailData.currency}</p>
            <p style="color: #6b7280; margin: 5px 0;"><strong>Submitted:</strong> ${emailData.timestamp}</p>
            <p style="color: #6b7280; margin: 5px 0;"><strong>Document ID:</strong> ${emailData.docId}</p>
          </div>
          
          <div style="background-color: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #374151; margin-top: 0;">Customer Message:</h3>
            <div style="background-color: #f3f4f6; padding: 15px; border-radius: 4px;">
              <p style="color: #374151; margin: 0; white-space: pre-wrap;">${emailData.message}</p>
            </div>
          </div>
          
          <div style="background-color: #dbeafe; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h4 style="color: #1e40af; margin-top: 0;">Quick Actions:</h4>
            <p style="color: #1e40af; margin: 5px 0;">
              • <a href="mailto:${emailData.customerEmail}" style="color: #1e40af;">Reply to customer</a>
            </p>
            <p style="color: #1e40af; margin: 5px 0;">
              • <a href="tel:${emailData.customerPhone}" style="color: #1e40af;">Call customer</a>
            </p>
            <p style="color: #1e40af; margin: 5px 0;">
              • <a href="https://console.firebase.google.com/project/YOUR_PROJECT_ID/firestore/data/contactSubmissions/${emailData.docId}" style="color: #1e40af;">View in Firestore</a>
            </p>
          </div>
        </div>
        
        <div style="background-color: #374151; color: white; padding: 20px; text-align: center; font-size: 14px;">
          <p style="margin: 0;">This is an automated notification from XYZ Company website.</p>
        </div>
      </div>
    `;

    const mailOptions = {
      from: functions.config().email.user, // Gmail email address
      to: functions.config().email.admin, // Admin email address
      subject: `New Contact Form: ${emailData.customerName} from ${emailData.customerLocation}`,
      html: adminEmailContent,
    };

    await transporter.sendMail(mailOptions);

    return { success: true };
  } catch (error) {
    console.log("ERROR: Error sending admin email", {
      error: error.message,
      stack: error.stack,
      adminEmail: functions.config().email.admin,
      timestamp: new Date().toISOString(),
    });
    return { success: false, error: error.message };
  }
}

// HTTP function for testing (optional)
exports.testEmail = functions.https.onRequest(async (req, res) => {
  try {
    const testData = {
      customerName: "Test User",
      customerEmail: "<EMAIL>",
      customerPhone: "+1234567890",
      customerLocation: "Test City, Test State, US",
      detectedLocation: "Test City, United States",
      message: "This is a test message from the contact form.",
      timezone: "America/New_York",
      currency: "USD",
      timestamp: new Date().toLocaleString(),
      docId: "test-doc-id",
    };

    const customerResult = await sendCustomerEmail(testData);
    const adminResult = await sendAdminEmail(testData);

    res.json({
      success: true,
      customerEmailSent: customerResult.success,
      adminEmailSent: adminResult.success,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});
